---
# Source: libra-parent/charts/libra-manager/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: libra-libra-manager-configmap
  namespace: galaxy
  labels:
    app.kubernetes.io/name: libra-manager
    app.kubernetes.io/instance: libra
    app.kubernetes.io/component: libra-manager
    app.kubernetes.io/managed-by: Helm
data:
  bootstrap-boxfat.yml: |
    server:
      #web端口
      port: 12083
    spring:
      cloud:
        nacos:
          discovery:
            server-addr: libra-libra-manager-service.galaxy.svc.cluster.local:8848
            prefer-ip-address: false  # 禁用IP注册
            use-hostname: true        # 使用K8s Service名称
      datasource:
        username: ENS_FAT
        password: ENC(7f003151f1d531102ab890b5513fb180)
        driver-class-name: oracle.jdbc.OracleDriver
        url: *******************************************
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
          #初始化物理连接个数
          initial-size: 20
          #最小连接池数量
          min-idle: 20
          #最大连接池数量
          max-active: 200
          # 配置获取连接等待超时的时间
          max-wait: 600000
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          time-between-eviction-runs-millis: 60000
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          min-evictable-idle-time-millis: 300000
          #用来检测连接是否有效的sql，要求是一个查询语句,如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用
          validation-query: SELECT 'X' FROM DUAL
          #申请连接的时候检测，如果空闲时间大于imeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
          test-while-idle: true
          #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          test-on-borrow: false
          #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
          test-on-return: false
          # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
          connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
          # 配置DruidStatFilter
          web-stat-filter:
            enabled: true
            url-pattern: /*
            exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
          # 配置DruidStatViewServlet
          stat-view-servlet:
            enabled: true
            url-pattern: /druid/*
            # IP白名单(没有配置或者为空，则允许所有访问)
            allow: 127.0.0.1
            # IP黑名单 (存在共同时，deny优先于allow)
            deny:
            # 禁用HTML页面上的"Reset All"功能
            reset-enable: false
            # 登录名
            login-username: admin
            # 登录密码
            login-password: 123456
          # 配置DruidStatFilter
          filter:
            stat:
              enabled: true
              # 慢SQL记录
              log-slow-sql: true
              slow-sql-millis: 1000
              merge-sql: true
            wall:
              enabled: true
            log4j:
              enabled: true
      jpa:
        hibernate:
          ddl-auto: update
        show-sql: true
        properties:
          hibernate:
            dialect: org.hibernate.dialect.Oracle12cDialect
            format_sql: true
      jackson:
        date-format: yyyy-MM-dd HH:mm:ss
        time-zone: GMT+8
      servlet:
        multipart:
          max-file-size: 10MB
          max-request-size: 10MB
    mybatis:
      #mybatis拓展属性文件路径
      config-location: classpath:mybatis/mybatis-config-oracle.xml
    
    sftp:
      client:
        protocol: shareFile
        # ip地址
        host: **************
        # 端口
        port: 22
        # 用户名
        username: fat
        # 密码
        password: fat202101
        # 根路径
        root: /home/<USER>/share/
        # 本地根路径
        lroot: /home/<USER>/share/
        # 密钥文件路径
        privateKey:
        # 密钥的密码
        passphrase: 
        #se
        sessionStrictHostKeyChecking: false
        # session连接超时时间
        sessionConnectTimeout: 15000
        # channel连接超时时间
        channelConnectedTimeout: 15000
        # 大文件拆分--拆分文件存放目录
        fileSplitDir: split
        # 大文件拆分--文件大小，单位MB，超过该配置大小在和sftp.client.fileSplitPercent配置项合并计算，决定是否拆分文件
        fileSplitSize: 2
        # 大文件拆分--超出范围百分比，单位%，1-100的数，超出设定的文件大小后，超出部分超出对应百分比后才进行拆分
        fileSplitPercent: 20
        # 大文件拆分--拆分后文件记录行数
        fileSplitLines: 20000
    
    ## nacos配置
    galaxy:
      tenantId: online-batch
      profile: boxfat
      appId: Libra
      appIdTwo: LIBRA-MANAGER
      # 所属数据中心,多中心部署时配置
      dataCenter: dc01
      #单元化
      logicUnitId: 
      phyUnitId:
---
# Source: libra-parent/charts/libra/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: libra-configmap
  namespace: galaxy
  labels:
    app.kubernetes.io/name: libra
    app.kubernetes.io/instance: libra
    app.kubernetes.io/component: libra
    app.kubernetes.io/managed-by: Helm
data:
  env_vars: |
    export CONFIG_TYPE='nacos'
    export IP_ADDRESS='http://nacosx-headless.paas.svc.cluster.local:8848'
    export CONFIG_NACOS_NAMESPACE='nebula_system'
    export CONFIG_NACOS_GROUP_ID='nebula.system.dc01'
    export ZOOKEEPER_CLUSTER_ID='libra-back'
    export JAVA_TOOL_OPTIONS=-javaagent:/app/dcits/app-run/galaxy/skywalking-agent/skywalking-agent/skywalking-agent.jar
    export SW_AGENT_NAME=vm@nebula@system::libra
    export SW_AGENT_INSTANCE_NAME=<EMAIL>
---
# Source: libra-parent/charts/libra-manager/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: libra-libra-manager-service
  namespace: galaxy
  labels:
    helm.sh/chart: libra-manager-0.1.0
    app.kubernetes.io/name: libra-manager
    app.kubernetes.io/instance: libra
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: libra-manager
    app.kubernetes.io/instance: libra
---
# Source: libra-parent/charts/libra/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: libra-service
  namespace: galaxy
  labels:
    helm.sh/chart: libra-0.1.0
    app.kubernetes.io/name: libra
    app.kubernetes.io/instance: libra
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: libra
    app.kubernetes.io/instance: libra
---
# Source: libra-parent/charts/libra-manager/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: libra-libra-manager
  namespace: galaxy
  labels:
    helm.sh/chart: libra-manager-0.1.0
    app.kubernetes.io/name: libra-manager
    app.kubernetes.io/instance: libra
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: libra-manager
      app.kubernetes.io/instance: libra
  template:
    metadata:
      labels:
        helm.sh/chart: libra-manager-0.1.0
        app.kubernetes.io/name: libra-manager
        app.kubernetes.io/instance: libra
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        {}
      containers:
        - name: libra-manager
          securityContext:
            {}
          image: "************.dkr.ecr.ap-southeast-1.amazonaws.com/galaxy/libra-manager:0.0.1-alpha"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 50m
              memory: 64Mi
          env:
            - name: PROFILES
              value: boxfat
---
# Source: libra-parent/charts/libra/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: libra
  namespace: galaxy
  labels:
    helm.sh/chart: libra-0.1.0
    app.kubernetes.io/name: libra
    app.kubernetes.io/instance: libra
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: libra
      app.kubernetes.io/instance: libra
  template:
    metadata:
      labels:
        helm.sh/chart: libra-0.1.0
        app.kubernetes.io/name: libra
        app.kubernetes.io/instance: libra
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      securityContext:
        {}
      initContainers:
      - command:
        - sh
        - -c
        - source /app/dcits/app-run/galaxy/libra/env_vars
        image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/onebank/busybox:latest
        imagePullPolicy: IfNotPresent
        name: init-dir
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - name: env-volume
          mountPath: /app/dcits/app-run/galaxy/libra
          subPath: env_vars
      containers:
        - name: libra
          securityContext:
            {}
          image: "************.dkr.ecr.ap-southeast-1.amazonaws.com/galaxy/libra:0.0.5-alpha"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 50m
              memory: 64Mi
          env:
            - name: PROFILES
              value: boxfat
          volumeMounts:
            - name: env-volume
              mountPath: /app/dcits/app-run/galaxy/libra
              subPath: env_vars
      volumes:
        - configMap:
            name: libra-configmap
          name: env-volume

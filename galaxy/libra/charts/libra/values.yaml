ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
replicaCount: 1

# Pod annotations
podAnnotations: {}

# Pod labels
podLabels: {}

# Pod security context
podSecurityContext: {}

# Container security context
securityContext: {}

# Image pull secrets
imagePullSecrets: []

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80
initImage:
  repository: "{{ .Values.global.imageHost }}/onebank/busybox"
  tag: "latest"
  pullPolicy: "IfNotPresent"
image:
  repository: "{{ .Values.global.imageHost }}/onebank/libra"
  tag: "0.0.5-alpha"
  pullPolicy: "IfNotPresent"
log:
  directory: "/logs/libra"
  volumeName: "log-volume"
  hostDirectory: "/home/<USER>/logs/libra"

# Service configuration
service:
  type: "ClusterIP"
  port: 8080
  targetPort: 8080

# Resource configuration
resources:
  limits:
    cpu: "500m"
    memory: "512Mi"
  requests:
    cpu: "50m"
    memory: "64Mi"

# Persistent Volume configuration
persistentVolume:
  enabled: false
  storageClass: "" # 如需指定存储类，填写名称，否则留空
  accessMode: "ReadWriteOnce"
  size: "1Gi"
  mountPath: "/app/data" # 容器内挂载路径

# Additional volumes
volumeMounts:
  - name: "env-volume"
    mountPath: "/app/dcits/app-run/galaxy/libra"
    subPath: "env_vars"
volumes:
  - name: "env-volume"
    configMap:
      name: "libra-configmap"

# # Liveness probe
# livenessProbe:
#   httpGet:
#     path: "/health"
#     port: 8080
#   initialDelaySeconds: 30
#   periodSeconds: 10

# # Readiness probe
# readinessProbe:
#   httpGet:
#     path: "/ready"
#     port: 8080
#   initialDelaySeconds: 5
#   periodSeconds: 5

# ConfigMap configuration
configMap:
  enabled: true
  data:
    env_vars: |
      export CONFIG_TYPE='nacos'
      export IP_ADDRESS='http://nacosx-headless.paas.svc.cluster.local:8848'
      export CONFIG_NACOS_NAMESPACE='nebula_system'
      export CONFIG_NACOS_GROUP_ID='nebula.system.dc01'
      export ZOOKEEPER_CLUSTER_ID='libra-back'
      export JAVA_TOOL_OPTIONS=-javaagent:/app/dcits/app-run/galaxy/skywalking-agent/skywalking-agent/skywalking-agent.jar
      export SW_AGENT_NAME=vm@nebula@system::libra
      export SW_AGENT_INSTANCE_NAME=<EMAIL>
